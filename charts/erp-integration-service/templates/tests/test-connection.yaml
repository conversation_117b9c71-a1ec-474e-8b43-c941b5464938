apiVersion: v1
kind: Pod
metadata:
  name: "{{ include "erp-integration-service.fullname" . }}-test-connection"
  labels:
    {{- include "erp-integration-service.labels" . | nindent 4 }}
  annotations:
    "helm.sh/hook": test
spec:
  containers:
    - name: wget
      image: busybox
      command: ['wget']
      args: ['{{ include "erp-integration-service.fullname" . }}:{{ .Values.service.port }}']
  restartPolicy: Never
