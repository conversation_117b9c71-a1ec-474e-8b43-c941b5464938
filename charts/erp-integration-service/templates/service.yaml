apiVersion: v1
kind: Service
metadata:
  name: {{ include "erp-integration-service.fullname" . }}
  labels:
    {{- include "erp-integration-service.labels" . | nindent 4 }}
spec:
  type: {{ .Values.service.type }}
  ports:
    - port: {{ .Values.service.port }}
      targetPort: http
      protocol: TCP
      name: http
  selector:
    {{- include "erp-integration-service.selectorLabels" . | nindent 4 }}
