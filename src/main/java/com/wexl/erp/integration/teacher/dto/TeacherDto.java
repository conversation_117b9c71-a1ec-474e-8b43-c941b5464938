package com.wexl.erp.integration.teacher.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;
import lombok.Builder;

public record TeacherDto() {

  @Builder
  public record TeacherRequest(String key) {}

  @Builder
  public record TeacherResponse(String status, int count, List<Teacher> data) {}

  @Builder
  public record Teacher(
      @JsonProperty("school_id") String schoolId,
      @JsonProperty("first_name") String firstName,
      @JsonProperty("middle_name") String middleName,
      @JsonProperty("last_name") String lastName,
      @JsonProperty("full_name") String fullName,
      @JsonProperty("teacher_id") String teacherId,
      @JsonProperty("teacher_email") String teacherEmail,
      @JsonProperty("teacher_mobile") String teacherMobile,
      @JsonProperty("residence_address") String residenceAddress,
      @JsonProperty("dob") String dob,
      @JsonProperty("date_of_joining") String dateOfJoining,
      @JsonProperty("gender") String gender,
      @JsonProperty("education_qualification") String educationQualification,
      @JsonProperty("father_name") String fatherName,
      @JsonProperty("spouse_name") String spouseName,
      @JsonProperty("class_teacher") String classTeacher) {}
}
