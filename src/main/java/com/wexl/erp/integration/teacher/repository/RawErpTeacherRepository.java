package com.wexl.erp.integration.teacher.repository;

import com.wexl.erp.integration.teacher.entity.RawErpTeacher;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

@Repository
public interface RawErpTeacherRepository extends JpaRepository<RawErpTeacher, Long> {
  @Transactional
  void deleteAllBySchoolId(String schoolId);
}
