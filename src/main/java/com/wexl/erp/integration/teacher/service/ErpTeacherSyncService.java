package com.wexl.erp.integration.teacher.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.wexl.erp.integration.teacher.dto.ErpData;
import com.wexl.erp.integration.teacher.dto.TeacherDto;
import com.wexl.erp.integration.teacher.entity.*;
import com.wexl.erp.integration.teacher.repository.ErpTeacherRepository;
import com.wexl.erp.integration.teacher.repository.RawErpTeacherRepository;
import com.wexl.erp.integration.teacher.util.ErpUtil;
import com.wexl.erp.integration.teacher.util.JsonUtil;
import java.util.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

@Service
@Slf4j
@RequiredArgsConstructor
public class ErpTeacherSyncService {
  @Value("${app.erp.teacher.url}")
  private String erpTeacherUrl;

  @Value("${app.erp.teacher.refreshfeed}")
  private boolean refreshTeacherFeed;

  private final ErpUtil erpUtil;
  private final RawErpTeacherRepository rawErpTeacherRepository;
  private final ErpTeacherRepository erpTeacherRepository;
  private final ErpJobService erpJobService;
  private final JsonUtil jsonUtil;
  private final RestTemplate restTemplate;

  public void syncTeachers(String orgSlug) {
    var erpJob = erpJobService.createJobEntry("TEACHER", orgSlug);
    String schoolId = erpUtil.getSchoolIdForOrgSlug(orgSlug);
    final List<TeacherDto.Teacher> allTeachers = getNewTeachers(schoolId);
    final Map<String, ErpTeacher> teacherByCodeMap = getTeacherByCodeMap(schoolId);
    process(allTeachers, schoolId, teacherByCodeMap, erpJob);
    deleteUnknownTeachers(erpJob);
    pushUpdatesToQueue(erpJob);
    erpJobService.markCompleted(erpJob);
  }

  private Map<String, ErpTeacher> getTeacherByCodeMap(String schoolId) {
    Map<String, ErpTeacher> teacherMap = new HashMap<>();
    for (ErpTeacher erpTeacher : erpTeacherRepository.findBySchoolId(schoolId)) {
      teacherMap.put(erpTeacher.getTeacherCode(), erpTeacher);
    }
    return teacherMap;
  }

  private List<TeacherDto.Teacher> getAllTeachers(String schoolId) {
    var uriComponent = UriComponentsBuilder.fromUriString(erpTeacherUrl).build().encode().toUri();

    HttpHeaders headers = new HttpHeaders();
    headers.setContentType(MediaType.APPLICATION_JSON);

    TeacherDto.TeacherRequest request = TeacherDto.TeacherRequest.builder().key(schoolId).build();
    HttpEntity<String> httpEntity = new HttpEntity<>(jsonUtil.toJson(request), headers);

    ResponseEntity<String> response =
        restTemplate.exchange(uriComponent, HttpMethod.POST, httpEntity, String.class);
    try {
      ObjectMapper mapper = new ObjectMapper();
      mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
      TeacherDto.TeacherResponse teacherData =
          mapper.readValue(response.getBody(), TeacherDto.TeacherResponse.class);
      if (teacherData == null || teacherData.data() == null || teacherData.data().isEmpty()) {
        log.error("No data fetched from the endpoint");
        return new ArrayList<>();
      }
      return teacherData.data().stream()
          .map(
              teacher ->
                  TeacherDto.Teacher.builder()
                      .schoolId(schoolId)
                      .firstName(teacher.firstName())
                      .lastName(teacher.lastName())
                      .middleName(teacher.middleName())
                      .fullName(teacher.fullName())
                      .teacherId(teacher.teacherId())
                      .teacherEmail(teacher.teacherEmail())
                      .teacherMobile(teacher.teacherMobile())
                      .residenceAddress(teacher.residenceAddress())
                      .dob(teacher.dob())
                      .dateOfJoining(teacher.dateOfJoining())
                      .gender(teacher.gender())
                      .educationQualification(teacher.educationQualification())
                      .fatherName(teacher.fatherName())
                      .spouseName(teacher.spouseName())
                      .classTeacher(teacher.classTeacher())
                      .build())
          .toList();
    } catch (JsonProcessingException e) {
      throw new RuntimeException(e);
    }
  }

  private List<TeacherDto.Teacher> getNewTeachers(String schoolId) {
    if (refreshTeacherFeed) {
      final List<TeacherDto.Teacher> allTeachers = getAllTeachers(schoolId);
      saveRawTeacherData(allTeachers, schoolId);
      return allTeachers;
    }
    return transformToTeachers(rawErpTeacherRepository.findAll());
  }

  private List<TeacherDto.Teacher> transformToTeachers(List<RawErpTeacher> rawErpTeachers) {
    return rawErpTeachers.stream()
        .map(
            rawErpTeacher -> {
              return TeacherDto.Teacher.builder()
                  .schoolId(rawErpTeacher.getSchoolId())
                  .firstName(rawErpTeacher.getFirstName())
                  .lastName(rawErpTeacher.getLastName())
                  .middleName(rawErpTeacher.getMiddleName())
                  .fullName(rawErpTeacher.getFullName())
                  .teacherId(rawErpTeacher.getTeacherId())
                  .teacherEmail(rawErpTeacher.getTeacherEmail())
                  .teacherMobile(rawErpTeacher.getTeacherMobile())
                  .residenceAddress(rawErpTeacher.getResidenceAddress())
                  .dob(rawErpTeacher.getDob())
                  .dateOfJoining(rawErpTeacher.getDateOfJoining())
                  .gender(rawErpTeacher.getGender())
                  .educationQualification(rawErpTeacher.getEducationQualification())
                  .fatherName(rawErpTeacher.getFatherName())
                  .spouseName(rawErpTeacher.getSpouseName())
                  .classTeacher(rawErpTeacher.getClassTeacher())
                  .build();
            })
        .toList();
  }

  private void saveRawTeacherData(List<TeacherDto.Teacher> allTeachers, String schoolId) {
    rawErpTeacherRepository.deleteAllBySchoolId(schoolId);
    rawErpTeacherRepository.saveAll(transformToRawErpTeachers(allTeachers, schoolId));
  }

  private List<RawErpTeacher> transformToRawErpTeachers(
      List<TeacherDto.Teacher> allTeachers, String schoolId) {
    return allTeachers.stream()
        .map(
            teacher -> {
              RawErpTeacher rawTeacher = new RawErpTeacher();
              rawTeacher.setSchoolId(schoolId);
              rawTeacher.setOrgSlug(erpUtil.getOrgSlugForSchoolId(schoolId));
              rawTeacher.setTeacherCode(constructTeacherCode(teacher));
              rawTeacher.setTeacherId(teacher.teacherId());
              rawTeacher.setFirstName(teacher.firstName());
              rawTeacher.setMiddleName(teacher.middleName());
              rawTeacher.setLastName(teacher.lastName());
              rawTeacher.setFullName(teacher.fullName());
              rawTeacher.setTeacherEmail(teacher.teacherEmail());
              rawTeacher.setTeacherMobile(teacher.teacherMobile());
              rawTeacher.setResidenceAddress(teacher.residenceAddress());
              rawTeacher.setDob(teacher.dob());
              rawTeacher.setDateOfJoining(teacher.dateOfJoining());
              rawTeacher.setGender(teacher.gender());
              rawTeacher.setEducationQualification(teacher.educationQualification());
              rawTeacher.setFatherName(teacher.fatherName());
              rawTeacher.setSpouseName(teacher.spouseName());
              rawTeacher.setClassTeacher(teacher.classTeacher());
              return rawTeacher;
            })
        .toList();
  }

  private void deleteUnknownTeachers(ErpJob erpJob) {
    log.info("Deleting unknown teachers now..");
    final List<String> teacherCodes =
        rawErpTeacherRepository.findAll().stream()
            .map(rawTeacher -> rawTeacher.getSchoolId() + "-" + rawTeacher.getTeacherId())
            .toList();
    final List<ErpTeacher> allByTeacherCodeNotIn =
        erpTeacherRepository.findAllByTeacherCodeNotIn(teacherCodes);
    allByTeacherCodeNotIn.forEach(
        teacher -> erpJobService.markTeacher(null, teacher, erpJob, "DELETE"));
    erpTeacherRepository.deleteAll(allByTeacherCodeNotIn);
    erpJobService.saveJob(erpJob);
    log.info("Deleting unknown teachers completed");
  }

  private void process(
      List<TeacherDto.Teacher> allTeachers,
      String schoolId,
      Map<String, ErpTeacher> teacherByCodeMap,
      ErpJob erpJob) {
    List<ErpTeacher> teachers = new ArrayList<>();
    for (TeacherDto.Teacher teacherFromFeed : allTeachers) {
      var teacherCode = constructTeacherCode(teacherFromFeed);
      if (teacherByCodeMap.containsKey(teacherCode)) {
        final ErpTeacher erpTeacher = teacherByCodeMap.get(teacherCode);
        if (isTeacherChanged(erpTeacher, teacherFromFeed)) {
          teachers.add(saveTeacher(erpTeacher, schoolId, teacherFromFeed));
          erpJobService.markTeacher(teacherFromFeed, erpTeacher, erpJob, "UPDATE");
        }
      } else {
        erpJobService.markTeacher(teacherFromFeed, null, erpJob, "ADD");
        teachers.add(saveTeacher(new ErpTeacher(), schoolId, teacherFromFeed));
      }
    }
    erpJobService.saveJob(erpJob);
    erpTeacherRepository.saveAll(teachers);
  }

  private boolean isTeacherChanged(ErpTeacher erpTeacher, TeacherDto.Teacher teacherFromFeed) {
    String[] fieldsToCheck =
        new String[] {
          "firstName",
          "lastName",
          "gender",
          "residenceAddress",
          "residencePhone",
          "email",
          "password"
        };
    for (String field : fieldsToCheck) {
      if (erpUtil.checkFieldsHaveDifferentValue(field, teacherFromFeed, erpTeacher)) {
        return true;
      }
    }
    return false;
  }

  private ErpTeacher saveTeacher(
      ErpTeacher erpTeacher, String schoolId, TeacherDto.Teacher jsonTeacher) {
    erpTeacher.setTeacherCode(constructTeacherCode(jsonTeacher));
    erpTeacher.setSchoolId(schoolId);
    erpTeacher.setOrgSlug(orgMapper(schoolId));
    erpTeacher.setTeacherId(jsonTeacher.teacherId());
    erpTeacher.setFirstName(jsonTeacher.firstName());
    erpTeacher.setMiddleName(jsonTeacher.middleName());
    erpTeacher.setLastName(jsonTeacher.lastName());
    erpTeacher.setFullName(jsonTeacher.fullName());
    erpTeacher.setTeacherEmail(jsonTeacher.teacherEmail());
    erpTeacher.setTeacherMobile(jsonTeacher.teacherMobile());
    erpTeacher.setResidenceAddress(jsonTeacher.residenceAddress());
    erpTeacher.setDob(jsonTeacher.dob());
    erpTeacher.setDateOfJoining(jsonTeacher.dateOfJoining());
    erpTeacher.setGender(jsonTeacher.gender());
    erpTeacher.setEducationQualification(jsonTeacher.educationQualification());
    erpTeacher.setFatherName(jsonTeacher.fatherName());
    erpTeacher.setSpouseName(jsonTeacher.spouseName());
    erpTeacher.setClassTeacher(jsonTeacher.classTeacher());
    return erpTeacher;
  }

  public String orgMapper(String schoolId) {
    return switch (schoolId) {
      case "SC1001" -> "erp237764";
      default -> "wexl-internal";
    };
  }

  private void pushUpdatesToQueue(ErpJob erpJob) {
    log.info("Pushing updates to retail service now");
    List<ErpData.ErpEntityChange> dpsEntityChangeList =
        erpJob.getJobDetails().stream()
            .map(
                c ->
                    ErpData.ErpEntityChange.builder()
                        .dateTime(String.valueOf(c.getCreatedAt()))
                        .id(c.getId())
                        .employeeCode(c.getRecordId())
                        .changeType(c.getOperation())
                        .teacherResponse(toTeacherResponse(c))
                        .type("teacher")
                        .build())
            .toList();
    final ErpData.ErpEntityChangeResponse teacherSnapshots =
        ErpData.ErpEntityChangeResponse.builder().erpEntityChanges(dpsEntityChangeList).build();
    erpUtil.pushToQueue(teacherSnapshots);
    log.info("Pushing updates to retail service completed");
  }

  private String constructTeacherCode(TeacherDto.Teacher teacherFromFeed) {
    return teacherFromFeed.schoolId() + "-" + teacherFromFeed.teacherId();
  }

  private ErpData.ErpTeacherResponse toTeacherResponse(ErpJobDetail c) {
    final String teacehrCode = c.getRecordId();
    final Optional<ErpTeacher> byTeacherId =
        erpTeacherRepository.findByTeacherCodeAndOrgSlug(teacehrCode, c.getJob().getOrgSlug());
    if (byTeacherId.isEmpty()) {
      return null;
    }
    var erpTeacher = byTeacherId.get();
    return ErpData.ErpTeacherResponse.builder()
        .id(erpTeacher.getId())
        .teacherName(erpTeacher.getFirstName() + " " + erpTeacher.getLastName())
        .teacherCode(erpTeacher.getTeacherCode())
        .email(erpTeacher.getTeacherEmail())
        .gender(erpTeacher.getGender())
        .schoolId(erpTeacher.getSchoolId())
        .orgSlug(erpTeacher.getOrgSlug())
        .build();
  }
}
