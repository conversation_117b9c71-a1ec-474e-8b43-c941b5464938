package com.wexl.erp.integration.teacher.entity;

import jakarta.persistence.*;
import java.sql.Timestamp;
import lombok.Data;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

@Entity
@Data
@Table(name = "erp_students")
@EntityListeners(AuditingEntityListener.class)
public class ErpStudent {
  @Id @GeneratedValue private int id;

  private String firstName;
  private String lastName;
  private String studentCode;
  private String studentEmailId;
  private String dob;

  private String gender;
  private String fatherName;
  private String fatherMobile;
  private String fatherEmail;
  private String motherName;
  private String motherMobile;
  private String motherEmail;
  private String classRollNum;
  private String studentAdmissionNo;
  @CreatedDate private Timestamp createdAt;
  @LastModifiedDate private Timestamp updatedAt;

  private String schoolId;
  private String orgSlug;
  private String gradeSlug;
  private String sectionUuid;
  private String sectionName;
}
