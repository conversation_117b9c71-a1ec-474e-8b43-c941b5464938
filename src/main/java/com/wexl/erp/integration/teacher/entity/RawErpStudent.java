package com.wexl.erp.integration.teacher.entity;

import jakarta.persistence.*;
import lombok.Data;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

@Entity
@Data
@Table(name = "raw_erp_students")
@EntityListeners(AuditingEntityListener.class)
public class RawErpStudent {
  @Id @GeneratedValue private int id;

  private String schoolId;
  private String orgSlug;
  private String firstName;
  private String middleName;
  private String lastName;
  private String fullName;
  private String studentMobile;
  private String studentEmail;
  private String fatherName;
  private String motherName;
  private String fatherMobile;
  private String motherMobile;
  private String fatherEmail;
  private String motherEmail;
  private String fatherOccupation;
  private String motherOccupation;
  private String studentAdmissionNo;
  private String classId;
  private int academicYear;
  private String gender;
  private String dob;
  private String classRollNum;
  private String dateOfAdmission;
  private String className;
  private int masterClassId;
  private String masterClassName;
}
