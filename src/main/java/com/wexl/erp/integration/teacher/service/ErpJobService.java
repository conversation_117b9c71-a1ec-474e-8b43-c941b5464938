package com.wexl.erp.integration.teacher.service;

import com.wexl.erp.integration.teacher.dto.Fee;
import com.wexl.erp.integration.teacher.dto.StudentDto;
import com.wexl.erp.integration.teacher.dto.TeacherDto;
import com.wexl.erp.integration.teacher.entity.*;
import com.wexl.erp.integration.teacher.repository.ErpJobRepository;
import com.wexl.erp.integration.teacher.repository.ErpStudentRepository;
import com.wexl.erp.integration.teacher.util.JsonUtil;
import java.util.HashSet;
import java.util.List;
import java.util.Optional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Service
@Slf4j
@RequiredArgsConstructor
public class ErpJobService {
  private static final String IN_PROGRESS = "IN_PROGRESS";
  private final JsonUtil jsonUtil;
  private final ErpJobRepository erpJobRepository;
  private final ErpStudentRepository erpStudentRepository;

  public ErpJob createJobEntry(String jobCategory, String orgSlug) {
    final ErpJob erpJob =
        ErpJob.builder()
            .jobCategory(jobCategory)
            .jobStatus(IN_PROGRESS)
            .jobDetails(new HashSet<>())
            .orgSlug(orgSlug)
            .build();
    return erpJobRepository.save(erpJob);
  }

  public void markTeacher(
      TeacherDto.Teacher teacherFromFeed, ErpTeacher erpTeacher, ErpJob erpJob, String update) {
    final ErpJobDetail erpJobDetail =
        ErpJobDetail.builder()
            .job(erpJob)
            .recordId(getTeacherCode(teacherFromFeed, erpTeacher))
            .currentRecord(erpTeacher != null ? jsonUtil.toJsonSafe(erpTeacher) : null)
            .updatedRecord(jsonUtil.toJsonSafe(teacherFromFeed))
            .operation(update)
            .build();
    erpJob.getJobDetails().add(erpJobDetail);
  }

  public void markStudent(
      StudentDto.Student studentFromFeed, ErpStudent erpStudent, ErpJob erpJob, String update) {
    final ErpJobDetail erpJobDetail =
        ErpJobDetail.builder()
            .job(erpJob)
            .recordId(getStudentCode(studentFromFeed, erpStudent))
            .currentRecord(erpStudent != null ? jsonUtil.toJsonSafe(erpStudent) : null)
            .updatedRecord(jsonUtil.toJsonSafe(studentFromFeed))
            .operation(update)
            .build();
    erpJob.getJobDetails().add(erpJobDetail);
  }

  public void markFeeStudent(Fee studentFromFeed, ErpFee erpStudent, ErpJob erpJob, String orgSlug, String update) {
    final ErpJobDetail erpJobDetail =
        ErpJobDetail.builder()
            .job(erpJob)
            .recordId(getFeeStudentCode(studentFromFeed, erpStudent, orgSlug))
            .currentRecord(erpStudent != null ? jsonUtil.toJsonSafe(erpStudent) : null)
            .updatedRecord(jsonUtil.toJsonSafe(studentFromFeed))
            .operation(update)
            .build();
    erpJob.getJobDetails().add(erpJobDetail);
  }

  public String trimDob(String dob) {
    return dob.replaceAll("[^0-9]", "");
  }

  private String getStudentCode(StudentDto.Student studentFromFeed, ErpStudent erpStudent) {
    if (studentFromFeed != null) {
      return studentFromFeed.studentAdmissionNo() + "-" + trimDob(studentFromFeed.dob());
    }
    if (erpStudent != null) {
      return erpStudent.getStudentCode();
    }
    return null;
  }

  private String getFeeStudentCode(Fee studentFromFeed, ErpFee erpFee, String orgSlug) {
    List<ErpStudent> erpStudent = erpStudentRepository.findByStudentAdmissionNoAndOrgSlug(studentFromFeed.getAdmissionNo(),orgSlug);
    if (!erpStudent.isEmpty()) {
      return studentFromFeed.getAdmissionNo() + "-" + trimDob(erpStudent.getFirst().getDob());
    }else {
      log.info("Student not found in ERP for admission no: {}", studentFromFeed.getAdmissionNo());
    }
    return null;
  }

  private String getTeacherCode(TeacherDto.Teacher teacherFromFeed, ErpTeacher erpTeacher) {
    if (teacherFromFeed != null) {
      return teacherFromFeed.schoolId() + "-" + teacherFromFeed.teacherId();
    }
    if (erpTeacher != null) {
      return erpTeacher.getTeacherCode();
    }
    return null;
  }

  public void saveJob(ErpJob erpJob) {
    erpJobRepository.save(erpJob);
  }

  public void markCompleted(ErpJob erpJob) {
    final Optional<ErpJob> byId = erpJobRepository.findById(erpJob.getId());
    if (byId.isEmpty()) {
      return;
    }
    final ErpJob erpJob1 = byId.get();
    erpJob1.setJobStatus("COMPLETED");
    erpJobRepository.save(erpJob1);
  }
}
