package com.wexl.erp.integration.teacher.util;

import com.wexl.erp.integration.teacher.dto.ErpData;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.reflect.FieldUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

@Slf4j
@Component
@RequiredArgsConstructor
public class ErpUtil {
  private final RestTemplate restTemplate;

  @Value("${app.teacherDevSyncUrl}")
  private String erpDevSyncUrl;

  @Value("${app.teacherSyncUrl}")
  private String erpSyncUrl;

  @Value("${app.syncProd:false}")
  private boolean syncProd;

  private static final Map<String, String> schoolIdMap = new HashMap<>();
  private static final Map<String, String> schoolIdMapReverse = new HashMap<>();
  private static final Map<String, String> studentSchoolIdMap = new HashMap<>();
  private static final Map<String, String> gradeMapper = new HashMap<>();

  static {
    gradeMapper.put("CLASS 1", "i");
    gradeMapper.put("CLASS 2", "ii");
    gradeMapper.put("CLASS 3", "iii");
    gradeMapper.put("CLASS 4", "iv");
    gradeMapper.put("CLASS 5", "v");
    gradeMapper.put("CLASS 6", "vi");
    gradeMapper.put("CLASS 7", "vii");
    gradeMapper.put("CLASS 8", "viii");
    gradeMapper.put("CLASS 9", "ix");
    gradeMapper.put("CLASS 10", "x");
    gradeMapper.put("CLASS 11", "xi");
    gradeMapper.put("CLASS 12", "xii");
    gradeMapper.put("NURSERY", "nur");
    gradeMapper.put("LKG", "lkg");
    gradeMapper.put("UKG", "ukg");
  }

  static {
    schoolIdMap.put("erp355557", "eed11d79f6d7510abc618dc7fdfa0593");
    schoolIdMap.put("the779154", "432b07d9cb0042057b0052401b469c81");
    schoolIdMap.put("doo784335", "b071551898327ee71b272a85a4e3f1c1");

    schoolIdMap.forEach((k, v) -> schoolIdMapReverse.put(v, k));
  }

  static {
    // studentSchoolIdMap.put("the677317", "U0MxMDAx");
    studentSchoolIdMap.put("the779154", "U0MxMDAy");
    studentSchoolIdMap.put("the677317", "U0MxMDAz");
    studentSchoolIdMap.put("the782042", "U0MxMDA0");
  }

  public String getGradeSlug(String grade) {
    return gradeMapper.get(grade);
  }

  public String getSchoolIdForOrgSlug(String orgSlug) {
    log.info("Getting school id for org slug: {}", orgSlug);
    return schoolIdMap.get(orgSlug);
  }

  public String getSchoolIdForStudentOrgSlug(String orgSlug) {
    return studentSchoolIdMap.get(orgSlug);
  }

  public String getOrgSlugForSchoolId(String schoolId) {
    return schoolIdMapReverse.get(schoolId);
  }

  public record Tuple<T, U>(T first, U second) {}

  public <T, U> Tuple<T, U> createTuple(T first, U second) {
    return new Tuple<>(first, second);
  }

  public static <T, U> T getFirst(Tuple<T, U> tuple) {
    return tuple.first;
  }

  public static <T, U> U getSecond(Tuple<T, U> tuple) {
    return tuple.second;
  }

  public boolean compareFirstAndSecondInTuple(List<Tuple<String, String>> tupleList) {
    boolean result = true;
    for (Tuple<String, String> stringStringTuple : tupleList) {
      result =
          result
              && checkTwoFieldsNotEqual(getFirst(stringStringTuple), getSecond(stringStringTuple));
    }
    return result;
  }

  public boolean checkTwoFieldsNotEqual(Object field1, Object field2) {
    if (field1 != null && field2 != null && !field1.equals(field2)) {
      return true;
    }
    if (field1 == null && field2 != null) {
      return true;
    }
    if (field1 != null && field2 == null) {
      return true;
    }
    return false;
  }

  public boolean checkFieldsHaveDifferentValue(String fieldName, Object source, Object target) {
    Object sourceField = readField(source, fieldName);
    Object targetField = readField(target, fieldName);
    return checkTwoFieldsNotEqual(sourceField, targetField);
  }

  private Object readField(Object source, String fieldName) {
    try {
      return FieldUtils.readField(source, fieldName, true);
    } catch (IllegalAccessException e) {
      log.error("Cannot read the field: {}", fieldName, e);
      return null;
    }
  }

  public void pushToQueue(ErpData.ErpEntityChangeResponse erpEntityChangeResponse) {
    String url = syncProd ? erpSyncUrl : erpDevSyncUrl;
    log.info("Sending a message to retail-service: {}", url);
    if (erpEntityChangeResponse.erpEntityChanges().isEmpty()) {
      log.info("No changes to sync");
      return;
    }

    final List<ErpData.ErpEntityChange> erpEntityChanges =
        erpEntityChangeResponse.erpEntityChanges();
    AtomicInteger counter = new AtomicInteger(0);
    splitErpEntityChangeList(erpEntityChanges)
        .forEach(
            c -> {
              sendToRetail(ErpData.ErpEntityChangeResponse.builder().erpEntityChanges(c).build());
              log.info("Running Batch: {}", counter.incrementAndGet());
              sleep();
            });
  }

  private List<List<ErpData.ErpEntityChange>> splitErpEntityChangeList(
      List<ErpData.ErpEntityChange> originalList) {
    int partitionSize = 100;
    List<List<ErpData.ErpEntityChange>> partitions = new LinkedList<>();
    for (int i = 0; i < originalList.size(); i += partitionSize) {
      partitions.add(originalList.subList(i, Math.min(i + partitionSize, originalList.size())));
    }
    return partitions;
  }

  private void sendToRetail(ErpData.ErpEntityChangeResponse erpEntityChangeResponse) {
    try {
      String url = syncProd ? erpSyncUrl : erpDevSyncUrl;
      log.info("Syncing to {}", syncProd ? "prod" : "dev");
      log.info("Using URL: {}", url);

      ResponseEntity<Void> response =
          restTemplate.postForEntity(url, erpEntityChangeResponse, Void.class);
      if (response.getStatusCode().is2xxSuccessful()) {
        log.info("Successfully sent the message to retail-service");
      } else {
        log.error("Failed to send the message to retail-service");
      }
    } catch (Exception ex) {
      log.error("Error while sending message to retail-service", ex);
    }
  }

  private void sleep() {
    try {
      log.info("Sleeping for 500ms");
      Thread.sleep(500);
      log.info("Waking up now!");
    } catch (InterruptedException e) {
      log.error("Error while sleeping", e);
    }
  }
}
