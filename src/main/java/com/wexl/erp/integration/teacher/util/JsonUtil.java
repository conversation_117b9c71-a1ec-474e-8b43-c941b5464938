package com.wexl.erp.integration.teacher.util;

import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.PropertyAccessor;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.wexl.erp.integration.teacher.entity.ErpJob;
import com.wexl.erp.integration.teacher.entity.ErpJobDetail;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class JsonUtil {
  private final ObjectMapper mapper;

  // Define mix-in classes to handle circular references
  @JsonIgnoreProperties({"jobDetails"})
  abstract static class ErpJobMixIn {}

  @JsonIgnoreProperties({"job"})
  abstract static class ErpJobDetailMixIn {}

  public JsonUtil() {
    mapper = new ObjectMapper();

    // Configure to handle circular references
    mapper.disable(SerializationFeature.FAIL_ON_EMPTY_BEANS);
    mapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
    mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);

    // Set visibility to handle circular references better
    mapper.setVisibility(PropertyAccessor.FIELD, JsonAutoDetect.Visibility.ANY);

    // Register mix-ins to handle circular references
    mapper.addMixIn(ErpJob.class, ErpJobMixIn.class);
    mapper.addMixIn(ErpJobDetail.class, ErpJobDetailMixIn.class);

    // This is a safer way to set the max nesting depth that works across Jackson versions
    System.setProperty("com.fasterxml.jackson.core.StreamWriteConstraints.maxNestingDepth", "2000");
  }

  public <T> String toJson(T body) {
    if (body == null) {
      return null;
    }
    try {
      return mapper.writeValueAsString(body);
    } catch (Exception e) {
      log.error("Error converting to json", e);
    }
    return null;
  }

  /**
   * Safely converts an object to JSON string, handling circular references. Use this method when
   * you know the object might have circular references.
   *
   * @param body The object to convert to JSON
   * @param <T> The type of the object
   * @return JSON string representation of the object, or null if conversion fails
   */
  public <T> String toJsonSafe(T body) {
    if (body == null) {
      return null;
    }
    try {
      // Create a copy of the mapper with identity reference detection enabled
      ObjectMapper safeMapper = mapper.copy();
      safeMapper.enable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS);

      // This will prevent infinite recursion by using object identity
      return safeMapper.writeValueAsString(body);
    } catch (Exception e) {
      log.error("Error safely converting to json", e);
      // If we still have issues, return a simplified representation
      try {
        return String.format("{\"%s\":\"%s\"}", body.getClass().getSimpleName(), body.toString());
      } catch (Exception ex) {
        log.error("Failed to create fallback JSON representation", ex);
      }
    }
    return null;
  }
}
