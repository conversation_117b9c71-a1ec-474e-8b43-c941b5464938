package com.wexl.erp.integration.teacher.dto;

import java.util.List;
import lombok.Builder;
import org.antlr.v4.runtime.misc.NotNull;

public record ErpData() {
  public record TeacherData(String status, List<Teacher> teachers, int count) {}

  @Builder
  public record ErpEntityChange(
      String changeType,
      Integer id,
      String dateTime,
      String employeeCode,
      String type,
      ErpTeacherResponse teacherResponse,
      ErpStudentResponse studentResponse) {}

  @Builder
  public record ErpEntityChangeResponse(List<ErpEntityChange> erpEntityChanges) {}

  @Builder
  public record ErpTeacherResponse(
      int id,
      String teacherName,
      @NotNull String teacherCode,
      String email,
      String phone,
      String gender,
      String schoolId,
      String branchName,
      String curriculum,
      String grade,
      String section,
      String orgSlug) {}

  @Builder
  public record ErpStudentResponse(
      int id,
      String firstName,
      String lastName,
      @NotNull String studentCode,
      String email,
      String phone,
      String branchName,
      String branchCode,
      String rollNumber,
      String curriculum,
      String grade,
      String sectionName,
      String gender,
      String classRollNo,
      String orgSlug,
      String gradeSlug,
      String sectionUuid,
      String fatherName,
      String motherName,
      String fatherPhone,
      String motherPhone,
      String fatherEmail,
      String motherEmail,
      String dob) {}
}
