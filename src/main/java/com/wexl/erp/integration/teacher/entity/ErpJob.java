package com.wexl.erp.integration.teacher.entity;

import jakarta.persistence.*;
import java.sql.Timestamp;
import java.util.LinkedHashSet;
import java.util.Set;
import lombok.*;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

@EqualsAndHashCode(exclude = {"jobDetails"})
@Entity
@Table(name = "erp_job")
@AllArgsConstructor
@NoArgsConstructor
@EntityListeners(AuditingEntityListener.class)
@Builder
@Data
public class ErpJob {
  @Id @GeneratedValue private int id;

  private String jobCategory; // student,teacher,parent

  @OneToMany(mappedBy = "job", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
  private Set<ErpJobDetail> jobDetails = new LinkedHashSet<>();

  @CreatedDate private Timestamp createdAt;

  @LastModifiedDate private Timestamp updatedAt;

  private String jobStatus;
  private String orgSlug;
}
