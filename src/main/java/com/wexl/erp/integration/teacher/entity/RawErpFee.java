package com.wexl.erp.integration.teacher.entity;

import jakarta.persistence.*;
import lombok.Data;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

@Entity
@Data
@Table(name = "raw_erp_fee")
@EntityListeners(AuditingEntityListener.class)
public class RawErpFee {
  @Id @GeneratedValue private int id;
  private String studentCode;
  private String studentName;
  private String dueAmount;
  private String admissionNo;
  private String rollNo;
  private String className;
  private String sectionName;
  private String academicYear;
  private String month;
  private String year;
  private String schoolId;
  private String orgSlug;
}
