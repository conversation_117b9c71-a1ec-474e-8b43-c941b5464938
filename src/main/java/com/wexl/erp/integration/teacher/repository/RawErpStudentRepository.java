package com.wexl.erp.integration.teacher.repository;

import com.wexl.erp.integration.teacher.entity.RawErpStudent;
import jakarta.transaction.Transactional;
import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface RawErpStudentRepository extends JpaRepository<RawErpStudent, Long> {
  List<RawErpStudent> findBySchoolId(String schoolId);

  @Transactional
  void deleteAllBySchoolId(String schoolId);
}
