package com.wexl.erp.integration.teacher.entity;

import jakarta.persistence.*;
import java.sql.Timestamp;
import lombok.*;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

@Entity
@Table(name = "erp_job_detail")
@EntityListeners(AuditingEntityListener.class)
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
public class ErpJobDetail {
  @Id @GeneratedValue private int id;

  @CreatedDate private Timestamp createdAt;
  @LastModifiedDate private Timestamp updatedAt;

  private String operation;

  @ManyToOne(cascade = CascadeType.ALL, fetch = FetchType.LAZY)
  private ErpJob job;

  @Column(columnDefinition = "TEXT")
  private String currentRecord;

  @Column(columnDefinition = "TEXT")
  private String updatedRecord;

  private String recordId;
}
