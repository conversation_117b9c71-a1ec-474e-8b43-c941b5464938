package com.wexl.erp.integration.teacher.repository;

import com.wexl.erp.integration.teacher.entity.ErpTeacher;
import java.util.List;
import java.util.Optional;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface ErpTeacherRepository extends JpaRepository<ErpTeacher, Long> {

  List<ErpTeacher> findBySchoolId(String schoolId);

  Optional<ErpTeacher> findByTeacherCodeAndOrgSlug(String teacherCode, String orgSlug);

  List<ErpTeacher> findAllByTeacherCodeNotIn(List<String> teacherCodes);
}
