package com.wexl.erp.integration.teacher.controller;

import com.wexl.erp.integration.teacher.service.ErpStudentSyncService;
import com.wexl.erp.integration.teacher.service.ErpTeacherSyncService;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@Slf4j
@RequiredArgsConstructor
public class SyncController {
  private final ErpTeacherSyncService erpTeacherSyncService;
  private final ErpStudentSyncService erpStudentSyncService;
  public static final List<String> STATIC_ORG_SLUGS =
      List.of("erp355557", "the779154", "doo784335");

  @PostMapping("/orgs/{orgSlug}/teacher:sync")
  public void sync(@PathVariable String orgSlug) {
    erpTeacherSyncService.syncTeachers(orgSlug);
  }

  @PostMapping("/orgs/{orgSlug}/student:sync")
  public void studentSync(@PathVariable String orgSlug) {
    log.info("Syncing students for org: {}", orgSlug);
    erpStudentSyncService.syncStudents(orgSlug);
  }

  @PostMapping("/orgs/{orgSlug}/fee:sync")
  public void feeSync(@PathVariable String orgSlug) {
    log.info("Syncing fee for org: {}", orgSlug);
    erpStudentSyncService.syncStudentFee(orgSlug);
  }

  // @Scheduled(cron = "0 0 11 * * ?", zone = "Asia/Kolkata")
  public void syncStudentsForMorning() {
    final DateFormat dateInstance = SimpleDateFormat.getDateInstance();
    log.info("Current server time: {}", LocalDateTime.now());
    log.info("Running Attendance Sync Job - {}", dateInstance.format(new java.util.Date()));
    for (String orgSlug : STATIC_ORG_SLUGS) {
      log.info("Syncing teachers for org: {}", orgSlug);
      erpStudentSyncService.syncStudents(orgSlug);
    }
  }

  // @Scheduled(cron = "0 0 17 * * ?", zone = "Asia/Kolkata")
  public void syncStudentsForEvening() {
    final DateFormat dateInstance = SimpleDateFormat.getDateInstance();
    log.info("Current server time: {}", LocalDateTime.now());
    log.info("Running Attendance Sync Job - {}", dateInstance.format(new java.util.Date()));
    for (String orgSlug : STATIC_ORG_SLUGS) {
      log.info("Syncing teachers for org: {}", orgSlug);
      erpStudentSyncService.syncStudents(orgSlug);
    }
  }
}
