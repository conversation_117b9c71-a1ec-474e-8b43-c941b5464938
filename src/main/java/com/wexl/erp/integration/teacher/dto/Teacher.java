package com.wexl.erp.integration.teacher.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
public class Teacher {
  @JsonProperty("id")
  private int id;

  @JsonProperty("school_id")
  private String schoolId;

  @JsonProperty("user_id")
  private String userId;

  @JsonProperty("teacher_id")
  private String teacherId;

  @JsonProperty("teacher_reg_id")
  private String teacherRegId;

  @JsonProperty("first_name")
  private String firstName;

  @JsonProperty("middle_name")
  private String middleName;

  @JsonProperty("last_name")
  private String lastName;

  @JsonProperty("date_of_joining")
  private String dateOfJoining;

  @JsonProperty("dob")
  private String dob;

  @JsonProperty("self_attendance")
  private String selfAttendance;

  @JsonProperty("gender")
  private String gender;

  @JsonProperty("blood_group")
  private String bloodGroup;

  @JsonProperty("father_name")
  private String fatherName;

  @JsonProperty("mother_name")
  private String motherName;

  @JsonProperty("house")
  private String house;

  @JsonProperty("residence_address")
  private String residenceAddress;

  @JsonProperty("residence_phone")
  private String residencePhone;

  @JsonProperty("image")
  private String image;

  @JsonProperty("status")
  private int status;

  @JsonProperty("last_working_date")
  private String lastWorkingDate;

  @JsonProperty("handover")
  private String handover;

  @JsonProperty("teacher_login_status")
  private String teacherLoginStatus;

  @JsonProperty("user_type")
  private String userType;

  @JsonProperty("password")
  private String password;

  @JsonProperty("email")
  private String email;

  @JsonProperty("pwd_update_date")
  private String pwdUpdateDate;

  @JsonProperty("gcm_id")
  private String gcmId;

  @JsonProperty("device_id")
  private String deviceId;

  @JsonProperty("biometric_id")
  private String biometricId;

  @JsonProperty("signature")
  private String signature;

  @JsonProperty("login_time")
  private String loginTime;

  @JsonProperty("bank_name")
  private String bankName;

  @JsonProperty("bank_account")
  private String bankAccount;

  @JsonProperty("ifsc_code")
  private String ifscCode;

  @JsonProperty("pf_no")
  private String pfNo;

  @JsonProperty("pt_no")
  private String ptNo;

  @JsonProperty("aadhar_number")
  private String aadharNumber;

  @JsonProperty("pan_number")
  private String panNumber;

  @JsonProperty("esi_number")
  private String esiNumber;

  @JsonProperty("uan_number")
  private String uanNumber;

  @JsonProperty("lic_number")
  private String licNumber;

  @JsonProperty("lic_validity")
  private String licValidity;

  @JsonProperty("religion")
  private String religion;

  @JsonProperty("caste")
  private String caste;

  @JsonProperty("sub_caste")
  private String subCaste;

  @JsonProperty("marital_status")
  private String maritalStatus;

  @JsonProperty("marriage_anniversary")
  private String marriageAnniversary;

  @JsonProperty("spouse_name")
  private String spouseName;

  @JsonProperty("child1_name")
  private String child1Name;

  @JsonProperty("child2_name")
  private String child2Name;

  @JsonProperty("child3_name")
  private String child3Name;

  @JsonProperty("child4_name")
  private String child4Name;

  @JsonProperty("education_qualification")
  private String educationQualification;

  @JsonProperty("official_email_id")
  private String officialEmailId;

  @JsonProperty("personal_email_id")
  private String personalEmailId;

  @JsonProperty("designation")
  private String designation;

  @JsonProperty("logout_time")
  private String logoutTime;

  @JsonProperty("alternate_phone")
  private String alternatePhone;

  @JsonProperty("teacher_type")
  private String teacherType;

  @JsonProperty("nationality")
  private String nationality;

  @JsonProperty("experience")
  private int experience;

  @JsonProperty("department_id")
  private int departmentId;

  @JsonProperty("employment_type")
  private String employmentType;

  @JsonProperty("title")
  private String title;

  @JsonProperty("role_id")
  private String roleId;

  @JsonProperty("police_verification_receipt")
  private String policeVerificationReceipt;

  @JsonProperty("date_of_transfer")
  private String dateOfTransfer;

  @JsonProperty("from_branch")
  private String fromBranch;

  @JsonProperty("to_branch")
  private String toBranch;

  @JsonProperty("block")
  private String block;

  @JsonProperty("role_responsibility")
  private String roleResponsibility;

  @JsonProperty("created_date")
  private String createdDate;

  @JsonProperty("modified_date")
  private String modifiedDate;

  @JsonProperty("handover_comment")
  private String handoverComment;

  @JsonProperty("department_name")
  private String departmentName;

  @JsonProperty("last_increament_date")
  private String lastIncreamentDate;

  @JsonProperty("increament_amount")
  private String increamentAmount;

  @JsonProperty("designation_name")
  private String designationNam;
}
