package com.wexl.erp.integration.teacher.dto;

import lombok.Data;

@Data
public class Student {
  private int id;
  private String schoolId;
  private String studentAdmNo;
  private String studentFirstName;
  private String studentMiddleName;
  private String studentLastName;
  private String studentFullName;
  private String gender;
  private String className;
  private String section;
  private String dateOfAdm;
  private String status;
  private String formFillingDate;
  private String studentType;
  private String hostelType;
  private String starInformation;
  private String concessionCategory;
  private String doNotDisturb;
  private String dateOfBirth;
  private String house;
  private String schoolEmailId;
  private String fathersName;
  private String fatherMobileNo;
  private String fatherWhatsAppNo;
  private String fatherEmailId;
  private String fatherOccupation;
  private String fatherDesignation;
  private String fatherOrganization;
  private String fatherAadhar;
  private String fatherIncome;
  private String mothersName;
  private String motherMobileNo;
  private String motherWhatsAppNo;
  private String motherEmailId;
  private String motherOccupation;
  private String motherOrganization;
  private String motherAadhar;
  private String residentialAddress;
  private String city;
  private String state;
  private String country;
  private String pincode;
  private String studentAadharNo;
  private String biometricId;
  private String bloodGroup;
  private String nationality;
  private String guardianNo;
  private String religion;
  private String caste;
  private String subCaste;
  private String motherTongue;
  private String bankReferenceNo;
  private String feeRemarks;
  private String foodHabbits;
  private String stateEnrollmentNo;
  private String feeProfileType;
  private String birthPlace;
  private String languageSpoken;
  private String sports;
  private String secondLanguage;
  private String thirdLanguage;
  private String casteCategory;
  private String bankName;
  private String ifscCode;
  private String bankAccountNo;
  private String joiningClass;
  private String previousSchool;
  private String identificationMarks1;
  private String identificationMarks2;
  private String transportRequired;
  private String description;
  private String joiningAcademicYear;
  private String tc;
  private String marksheet;
  private String dobCertificate;
  private String classRollNo;
  private String whatsAppNo;
  private String schoolAdmissionNo;
  private String previousClass;
  private String studentEmailId;
  private String previousBoardOfEducation;
  private String rteStudent;
  private String permanentEducationNumber;
  private String udiseNumber;
  private String apaarId;
}
