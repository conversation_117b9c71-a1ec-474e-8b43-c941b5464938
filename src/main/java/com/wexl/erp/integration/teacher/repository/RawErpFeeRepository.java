package com.wexl.erp.integration.teacher.repository;

import com.wexl.erp.integration.teacher.entity.RawErpFee;
import jakarta.transaction.Transactional;
import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface RawErpFeeRepository extends JpaRepository<RawErpFee, Long> {
  List<RawErpFee> findBySchoolId(String schoolId);

  @Transactional
  void deleteAllBySchoolId(String schoolId);
}
