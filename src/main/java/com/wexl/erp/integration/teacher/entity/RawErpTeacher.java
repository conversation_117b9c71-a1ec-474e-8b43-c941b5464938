package com.wexl.erp.integration.teacher.entity;

import jakarta.persistence.*;
import java.sql.Timestamp;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

@Entity
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "raw_erp_teacher")
@EntityListeners(AuditingEntityListener.class)
public class RawErpTeacher {
  @Id @GeneratedValue private int id;

  @CreatedDate private Timestamp createdAt;
  @LastModifiedDate private Timestamp updatedAt;
  private String schoolId;
  private String orgSlug;
  private String teacherCode;

  private String teacherId;
  private String firstName;
  private String middleName;
  private String lastName;
  private String fullName;
  private String teacherEmail;
  private String teacherMobile;
  private String residenceAddress;
  private String dob;
  private String dateOfJoining;
  private String gender;
  private String educationQualification;
  private String fatherName;
  private String spouseName;
  private String classTeacher;
}
