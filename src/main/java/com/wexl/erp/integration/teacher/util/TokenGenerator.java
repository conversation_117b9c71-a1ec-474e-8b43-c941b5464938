package com.wexl.erp.integration.teacher.util;

import io.jsonwebtoken.JwtBuilder;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.SignatureAlgorithm;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.Base64;
import java.util.Date;
import java.util.List;
import javax.crypto.spec.SecretKeySpec;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
@Slf4j
public class TokenGenerator {

  @Value("${jwt.tokenSecret}")
  private String tokenSecret;

  public static final String IS_MOBILE = "mob";
  public static final String ORGANIZATION = "organization";
  public static final String ID = "id";

  private static final String ROLES = "roles";

  private static final String STUDENT_ID = "studentId";

  public static final String SCOPE = "scope";

  public static final String LOGIN_METHOD = "loginMethod";
  public static final String CLASS_ID = "classId";
  public static final String BOARD_ID = "boardId";
  public static final String FIRST_NAME = "firstName";
  public static final String LAST_NAME = "lastName";
  public static final String MOBILE_NUMBER = "mobileNumber";
  public static final String EMAIL = "email";
  public static final String TEACHER_DETAILS_ID = "teacherDetailsId";
  public static final String VERIFICATION_STATUS = "verificationStatus";

  public String generateStudentToken(String org, String username, long studentId, Long id) {
    try {
      final String token = getUserJwtBuilder(org, username, studentId, id).compact();
      log.info("Generated token for user: {}", username);
      return token;
    } catch (Exception e) {
      throw new IllegalArgumentException("Failed to generate token", e);
    }
  }

  private JwtBuilder getUserJwtBuilder(String org, String username, long studentId, Long id) {
    var tokenSignInKey =
        new SecretKeySpec(
            Base64.getDecoder().decode(tokenSecret), SignatureAlgorithm.HS256.getJcaName());
    var currentDate = Instant.now();
    return Jwts.builder()
        .claim(ID, id)
        .claim(ORGANIZATION, org)
        .claim(IS_MOBILE, false)
        .claim(STUDENT_ID, studentId)
        .claim(SCOPE, "SCHEDULED_TEST")
        .claim(LOGIN_METHOD, "system_created")
        .claim(CLASS_ID, 1)
        .claim(BOARD_ID, 1)
        .setSubject(username)
        .setIssuedAt(Date.from(currentDate))
        .setExpiration(Date.from(currentDate.plus(30, ChronoUnit.DAYS)))
        .claim(ROLES, List.of("ROLE_ISTUDENT"))
        .signWith(tokenSignInKey, SignatureAlgorithm.HS256);
  }

  public String generateAdminToken() {
    try {
      return getUserAdminJwtBuilder().compact();
    } catch (Exception e) {
      throw new IllegalArgumentException("Failed to generate token", e);
    }
  }

  private JwtBuilder getUserAdminJwtBuilder() {
    var tokenSignInKey =
        new SecretKeySpec(
            Base64.getDecoder().decode(tokenSecret), SignatureAlgorithm.HS256.getJcaName());
    var currentDate = Instant.now();
    return Jwts.builder()
        .claim(ID, 3)
        .claim(FIRST_NAME, "retail_service_user")
        .claim(LAST_NAME, "Bot")
        .claim(EMAIL, "<EMAIL>")
        .claim(ORGANIZATION, "wexl-internal")
        .claim(SCOPE, "3D_VIDEOS ACTIVITY_FEED")
        .claim(IS_MOBILE, false)
        .claim(LOGIN_METHOD, "bot")
        .claim(MOBILE_NUMBER, "9100000000")
        .claim(ROLES, List.of("ROLE_ITEACHER", "ROLE_ADMIN", "ROLE_ORG_ADMIN"))
        .claim(TEACHER_DETAILS_ID, 3)
        .claim(VERIFICATION_STATUS, "VERIFIED")
        .setSubject("38f73cf8-a815-4ec2-a394-30af9af988aa")
        .setId("3")
        .setIssuedAt(Date.from(currentDate))
        .setExpiration(Date.from(currentDate.plus(2, ChronoUnit.DAYS)))
        .signWith(tokenSignInKey, SignatureAlgorithm.HS256);
  }
}
