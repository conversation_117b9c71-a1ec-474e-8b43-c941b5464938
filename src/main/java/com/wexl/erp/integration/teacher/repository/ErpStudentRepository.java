package com.wexl.erp.integration.teacher.repository;

import com.wexl.erp.integration.teacher.entity.ErpStudent;
import jakarta.transaction.Transactional;
import java.util.Collection;
import java.util.List;
import java.util.Optional;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface ErpStudentRepository extends JpaRepository<ErpStudent, Long> {
  List<ErpStudent> findBySchoolId(String schoolId);

  Optional<ErpStudent> findByStudentCodeAndOrgSlug(String studentCode, String orgSlug);

  List<ErpStudent> findAllByStudentCodeNotIn(Collection<String> studentCodes);
  List<ErpStudent> findByStudentAdmissionNoAndOrgSlug(String admissionNo, String schoolId);

  @Transactional
  void deleteAllBySchoolId(String schoolId);
}
