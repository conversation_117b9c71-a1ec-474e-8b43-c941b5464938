package com.wexl.erp.integration.teacher.dto;

import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.Id;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Entity
@AllArgsConstructor
@NoArgsConstructor
public class SectionMapper {
  @Id @GeneratedValue private int id;

  private String branchCode;

  private String board;

  private String erpGrade;

  private String erpSection;

  private String gradeSlug;

  private String orgSlug;

  private String sectionUuid;

  private String sectionName;
}
