package com.wexl.erp.integration.teacher.service;

import com.wexl.erp.integration.teacher.ErpIntegrationException;
import com.wexl.erp.integration.teacher.dto.Fee;
import com.wexl.erp.integration.teacher.dto.Student;
import java.io.ByteArrayInputStream;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import org.apache.poi.ss.usermodel.*;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.Resource;
import org.springframework.stereotype.Component;

@Component
public class ExcelReader {
  @Value("classpath:I-Bulbul.xlsx")
  private Resource studentDataExcel;

  public List<Student> extractStudentData(byte[] excelBytes) {
    List<Student> students = new ArrayList<>();
    try (InputStream student1 = new ByteArrayInputStream(excelBytes);
        Workbook workbook = WorkbookFactory.create(student1)) {
      Sheet sheet = workbook.getSheetAt(0);

      // Skip the header row (first row)
      Iterator<Row> rowIterator = sheet.iterator();
      if (rowIterator.hasNext()) {
        // Skip header
        rowIterator.next();
      }

      // Process data rows
      while (rowIterator.hasNext()) {
        Row row = rowIterator.next();
        Student student = createStudentFromRow(row);
        if (student != null) {
          students.add(student);
        }
      }
    } catch (Exception e) {
      throw new ErpIntegrationException("Failed to extract student data from Excel", e);
    }
    return students;
  }

  private Student createStudentFromRow(Row row) {
    // Skip empty rows
    if (row == null || isEmptyRow(row)) {
      return null;
    }

    Student student = new Student();

    // Map each cell to the corresponding Student property
    student.setStudentAdmNo(getCellValueAsString(row.getCell(0)));
    student.setStudentFirstName(getCellValueAsString(row.getCell(1)));
    student.setStudentMiddleName(getCellValueAsString(row.getCell(2)));
    student.setStudentLastName(getCellValueAsString(row.getCell(3)));
    student.setStudentFullName(getCellValueAsString(row.getCell(4)));
    student.setGender(getCellValueAsString(row.getCell(5)));
    student.setClassName(getCellValueAsString(row.getCell(6)));
    student.setSection(getCellValueAsString(row.getCell(7)));
    student.setDateOfAdm(getCellValueAsString(row.getCell(8)));
    student.setStatus(getCellValueAsString(row.getCell(9)));
    student.setFormFillingDate(getCellValueAsString(row.getCell(10)));
    student.setStudentType(getCellValueAsString(row.getCell(11)));
    student.setHostelType(getCellValueAsString(row.getCell(12)));
    student.setStarInformation(getCellValueAsString(row.getCell(13)));
    student.setConcessionCategory(getCellValueAsString(row.getCell(14)));
    student.setDoNotDisturb(getCellValueAsString(row.getCell(15)));
    student.setDateOfBirth(getCellValueAsString(row.getCell(16)));
    student.setHouse(getCellValueAsString(row.getCell(17)));
    student.setSchoolEmailId(getCellValueAsString(row.getCell(18)));
    student.setFathersName(getCellValueAsString(row.getCell(19)));
    student.setFatherMobileNo(getCellValueAsString(row.getCell(20)));
    student.setFatherWhatsAppNo(getCellValueAsString(row.getCell(21)));
    student.setFatherEmailId(getCellValueAsString(row.getCell(22)));
    student.setFatherOccupation(getCellValueAsString(row.getCell(23)));
    student.setFatherDesignation(getCellValueAsString(row.getCell(24)));
    student.setFatherOrganization(getCellValueAsString(row.getCell(25)));
    student.setFatherAadhar(getCellValueAsString(row.getCell(26)));
    student.setFatherIncome(getCellValueAsString(row.getCell(27)));
    student.setMothersName(getCellValueAsString(row.getCell(28)));
    student.setMotherMobileNo(getCellValueAsString(row.getCell(29)));
    student.setMotherWhatsAppNo(getCellValueAsString(row.getCell(30)));
    student.setMotherEmailId(getCellValueAsString(row.getCell(31)));
    student.setMotherOccupation(getCellValueAsString(row.getCell(32)));
    student.setMotherOrganization(getCellValueAsString(row.getCell(33)));
    student.setMotherAadhar(getCellValueAsString(row.getCell(34)));
    student.setResidentialAddress(getCellValueAsString(row.getCell(35)));
    student.setCity(getCellValueAsString(row.getCell(36)));
    student.setState(getCellValueAsString(row.getCell(37)));
    student.setCountry(getCellValueAsString(row.getCell(38)));
    student.setPincode(getCellValueAsString(row.getCell(39)));
    student.setStudentAadharNo(getCellValueAsString(row.getCell(40)));
    student.setBiometricId(getCellValueAsString(row.getCell(41)));
    student.setBloodGroup(getCellValueAsString(row.getCell(42)));
    student.setNationality(getCellValueAsString(row.getCell(43)));
    student.setGuardianNo(getCellValueAsString(row.getCell(44)));
    student.setReligion(getCellValueAsString(row.getCell(45)));
    student.setCaste(getCellValueAsString(row.getCell(46)));
    student.setSubCaste(getCellValueAsString(row.getCell(47)));
    student.setMotherTongue(getCellValueAsString(row.getCell(48)));
    student.setBankReferenceNo(getCellValueAsString(row.getCell(49)));
    student.setFeeRemarks(getCellValueAsString(row.getCell(50)));
    student.setFoodHabbits(getCellValueAsString(row.getCell(51)));
    student.setStateEnrollmentNo(getCellValueAsString(row.getCell(52)));
    student.setFeeProfileType(getCellValueAsString(row.getCell(53)));
    student.setBirthPlace(getCellValueAsString(row.getCell(54)));
    student.setLanguageSpoken(getCellValueAsString(row.getCell(55)));
    student.setSports(getCellValueAsString(row.getCell(56)));
    student.setSecondLanguage(getCellValueAsString(row.getCell(57)));
    student.setThirdLanguage(getCellValueAsString(row.getCell(58)));
    student.setCasteCategory(getCellValueAsString(row.getCell(59)));
    student.setBankName(getCellValueAsString(row.getCell(60)));
    student.setIfscCode(getCellValueAsString(row.getCell(61)));
    student.setBankAccountNo(getCellValueAsString(row.getCell(62)));
    student.setJoiningClass(getCellValueAsString(row.getCell(63)));
    student.setPreviousSchool(getCellValueAsString(row.getCell(64)));
    student.setIdentificationMarks1(getCellValueAsString(row.getCell(65)));
    student.setIdentificationMarks2(getCellValueAsString(row.getCell(66)));
    student.setTransportRequired(getCellValueAsString(row.getCell(67)));
    student.setDescription(getCellValueAsString(row.getCell(68)));
    student.setJoiningAcademicYear(getCellValueAsString(row.getCell(69)));
    student.setTc(getCellValueAsString(row.getCell(70)));
    student.setMarksheet(getCellValueAsString(row.getCell(71)));
    student.setDobCertificate(getCellValueAsString(row.getCell(72)));
    student.setClassRollNo(getCellValueAsString(row.getCell(73)));
    student.setWhatsAppNo(getCellValueAsString(row.getCell(74)));
    student.setSchoolAdmissionNo(getCellValueAsString(row.getCell(75)));
    student.setPreviousClass(getCellValueAsString(row.getCell(76)));
    student.setStudentEmailId(getCellValueAsString(row.getCell(77)));
    student.setPreviousBoardOfEducation(getCellValueAsString(row.getCell(78)));
    student.setRteStudent(getCellValueAsString(row.getCell(79)));
    student.setPermanentEducationNumber(getCellValueAsString(row.getCell(80)));
    student.setUdiseNumber(getCellValueAsString(row.getCell(81)));
    student.setApaarId(getCellValueAsString(row.getCell(82)));

    return student;
  }

  private boolean isEmptyRow(Row row) {
    if (row.getCell(0) == null) {
      return true;
    }
    return getCellValueAsString(row.getCell(0)).trim().isEmpty();
  }

  private String getCellValueAsString(Cell cell) {
    if (cell == null) {
      return "";
    }

    switch (cell.getCellType()) {
      case STRING:
        return cell.getStringCellValue();
      case NUMERIC:
        if (DateUtil.isCellDateFormatted(cell)) {
          return cell.getDateCellValue().toString();
        } else {
          // To handle numbers without decimal points
          double value = cell.getNumericCellValue();
          if (value == (long) value) {
            return String.valueOf((long) value);
          } else {
            return String.valueOf(value);
          }
        }
      case BOOLEAN:
        return Boolean.toString(cell.getBooleanCellValue());
      case FORMULA:
        try {
          return cell.getStringCellValue();
        } catch (Exception e) {
          try {
            return String.valueOf(cell.getNumericCellValue());
          } catch (Exception ex) {
            return "";
          }
        }
      default:
        return "";
    }
  }

  public List<Fee> extractStudentFeeData(byte[] excelBytes) {
    List<Fee> fees = new ArrayList<>();
    try (InputStream student1 = new ByteArrayInputStream(excelBytes);
        Workbook workbook = WorkbookFactory.create(student1)) {
      Sheet sheet = workbook.getSheetAt(0);

      // Skip the header row (first row)
      Iterator<Row> rowIterator = sheet.iterator();
      if (rowIterator.hasNext()) {
        // Skip header
        rowIterator.next();
      }

      // Process data rows
      while (rowIterator.hasNext()) {
        Row row = rowIterator.next();
        Fee fee = createFeeFromRow(row);
        if (fee != null) {
          fees.add(fee);
        }
      }
    } catch (Exception e) {
      throw new ErpIntegrationException("Failed to extract student data from Excel", e);
    }
    return fees;
  }

  private Fee createFeeFromRow(Row row) {
    // Skip empty rows
    if (row == null || isEmptyRow(row)) {
      return null;
    }
    return Fee.builder()
            .studentName(getCellValueAsString(row.getCell(0)))
            .admissionNo(getCellValueAsString(row.getCell(1)))
            .rollNo(getCellValueAsString(row.getCell(2)))
            .className(getCellValueAsString(row.getCell(3)))
            .dueAmount(getCellValueAsString(row.getCell(31)))
            .build();
  }
}
