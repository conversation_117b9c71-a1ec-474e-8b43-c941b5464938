package com.wexl.erp.integration.teacher.repository;

import com.wexl.erp.integration.teacher.entity.ErpFee;
import java.util.List;
import java.util.Optional;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface ErpFeeRepository extends JpaRepository<ErpFee, Long> {
  List<ErpFee> findAllByStudentCodeNotIn(List<String> studentCodes);

  List<ErpFee> findBySchoolId(String schoolId);

  Optional<ErpFee> findByStudentCodeAndOrgSlug(String studentId, String orgSlug);
}
