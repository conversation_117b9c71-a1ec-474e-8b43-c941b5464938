package com.wexl.erp.integration.teacher.entity;

import jakarta.persistence.*;
import java.sql.Timestamp;
import lombok.Data;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

@Entity
@Data
@Table(name = "erp_teacher")
@EntityListeners(AuditingEntityListener.class)
public class ErpTeacher {
  @Id @GeneratedValue private int id;
  private String schoolId;
  private String orgSlug;
  private String teacherCode;

  private String teacherId;
  private String firstName;
  private String middleName;
  private String lastName;
  private String fullName;
  private String teacherEmail;
  private String teacherMobile;
  private String residenceAddress;
  private String dob;
  private String dateOfJoining;
  private String gender;
  private String educationQualification;
  private String fatherName;
  private String spouseName;
  private String classTeacher;
  @CreatedDate private Timestamp createdAt;
  @LastModifiedDate private Timestamp updatedAt;
}
