spring:
  application:
    name: erp-integration-service
  datasource:
    url: ********************************************************************
    username: erp_user
    password: hsyr434fFGsd4
  jpa:
    properties:
      hibernate:
        order_inserts: true
        jdbc:
          batch_size: 100
        format_sql: true
    hibernate:
      ddl-auto: none
server:
  servlet:
    context-path: /api
jwt:
  tokenSecret: HHGDsdfdsfjk344fjs73jfh47984sdsdf4GSjskG273owyrbnsjrt46Ghdshdj29837HDjsdjhfdjh736567
app:
  syncProd: false
  teacherDevSyncUrl: https://learn.academyteacher.com/api/public/erp-teacher:sync
  teacherSyncUrl: https://console.wexledu.com/api/public/erp-teacher:sync
  erp:
    student:
      url: https://erp.wexledu.com/API/WeXL/get_students_details.php
    teacher:
      url: https://erp.wexledu.com//API/WeXL/get_teacher_details.php
      refreshfeed: true

management:
  endpoints:
    web:
      # By default, the actuator base path is "/actuator", so combined with context-path "/api",
      # your health endpoint is at "/api/actuator/health"
      exposure:
        # Expose whichever endpoints you need: health, prometheus, info, etc.
        include: health, prometheus

  endpoint:
    # Config for 'health' endpoint goes here
    health:
      show-details: always   # or "when_authorized" / "never" based on security concerns

  health:
    mail:
      enabled: false         # You can disable checks you don't need
    livenessState:
      enabled: true          # In Spring Boot 2.3+, liveness/readiness are top-level health indicators
    readinessState:
      enabled: true
    probes:
      enabled: true
