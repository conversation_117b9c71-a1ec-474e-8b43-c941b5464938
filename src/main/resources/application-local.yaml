spring:
  application:
    name: erp-integration-service
  datasource:
    url: ********************************************************************
    username: erp_user
    password: hsyr434fFGsd4
  jpa:
    hibernate:
      ddl-auto: update
  servlet:
    server:
      port: 8070
    context-path: /api
  endpoints:
    web:
      exposure:
        include: prometheus,health
  health:
    mail:
      enabled: false
app:
  syncProd: false
  teacherDevSyncUrl: https://learn.academyteacher.com/api/public/erp-teacher:sync
  teacherSyncUrl: https://console.wexledu.com/api/public/erp-teacher:sync
  erp:
    student:
      url: https://erp.wexledu.com/API/WeXL/get_students_details.php
    teacher:
      url: https://erp.wexledu.com//API/WeXL/get_teacher_details.php
      refreshfeed: true